from contextlib import asynccontextmanager

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from fastapi import FastAPI

from src.helper.client.milvus import hybrid_search_optimized
from src.log import logger

from src.tasks.tasks import update_knowledge_base

# 创建调度器
scheduler = BackgroundScheduler()


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting scheduler")
    scheduler.add_job(
        update_knowledge_base,
        IntervalTrigger(hours=10),
        id="update_knowledge_base",
        name="Update knowledge base every hour",
        replace_existing=True,
    )
    logger.info("add job update_knowledge_base")
    scheduler.start()  # 启动调度器
    yield
    logger.info("Stopping scheduler")
    scheduler.shutdown()  # 关闭调度器


# FastAPI 应用
app = FastAPI(lifespan=lifespan)

if __name__ == "__main__":
    import uvicorn

    update_knowledge_base()

    query_text = "数据库预案"
    results = hybrid_search_optimized(query_text, "knowledge_base_of_sre_v3_test")

    uvicorn.run(app, host="0.0.0.0", port=8000)
