import os
from functools import cache
from typing import List

from langchain_core.documents import Document
from pymilvus import MilvusClient, DataType

from settings import get_or_creat_settings_ins
from src.helper.client.rerank import rerank
from src.log import logger
from src.modules.llms import LLMFactory, EmbeddingType


@cache
def get_milvus_client() -> MilvusClient:
    config = get_or_creat_settings_ins()
    milvus_client = MilvusClient(
        uri=config.milvus.uri,
        token=f"{config.milvus.user}:{os.getenv('MILVUS__PASSWORD')}",
        db_name=config.milvus.db_name,
        timeout=config.milvus.timeout,
    )
    return milvus_client


def create_collection(collection_name):
    milvus_client = get_milvus_client()
    list_collections = milvus_client.list_collections()
    if collection_name not in list_collections:
        schema = milvus_client.create_schema(
            auto_id=True,
            enable_dynamic_field=True
        )
        schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field(field_name="source", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="title", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="token", datatype=DataType.VARCHAR, max_length=255)
        schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=2048, enable_analyzer=True,
                         enable_match=True)
        schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=65535)
        schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=768)

        # 索引参数
        index_params = milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 4096}
        )
        milvus_client.create_collection(
            collection_name=collection_name,
            schema=schema,
            index_params=index_params
        )
        logger.info(f"collection {collection_name} created")


def delete_by_source(collection_name: str, source: str):
    results = get_milvus_client().query(
        collection_name=collection_name,
        filter=f"source=='{source}'",
        output_fields=["id"]
    )
    logger.info(f"delete file: {source}")
    if len(results) > 0:
        ids = [data["id"] for data in results]
        get_milvus_client().delete(
            collection_name=collection_name,
            ids=ids
        )


def hybrid_search_optimized(query_text: str, collection_name: str) -> List[Document]:
    try:
        return get_document(query_text, collection_name)
    except Exception as e:
        logger.error(f"hybrid search error: {e}")
    return []


def get_document(query_text: str, collection_name) -> List[Document]:
    milvus_client = get_milvus_client()
    hits = milvus_client.search(
        collection_name=collection_name,
        data=[get_query_embedding(query_text)],
        limit=10,
        anns_field="vector",
        search_params={
            "metric_type": "COSINE",
            "params": {
                "nprobe": 1024,
                "radius": 0.6,
                "range_filter": 10,
            },
        },
    )[0]
    if not hits:
        # TODO：如果搜索结果为空，则使用全文检索
        # filter = "TEXT_MATCH(text, 'keyword1 keyword2')"
        #
        # result = client.search(
        #     collection_name="YOUR_COLLECTION_NAME", # Your collection name
        #     anns_field="embeddings", # Vector field name
        #     data=[query_vector], # Query vector
        #     filter=filter,
        #     search_params={"params": {"nprobe": 10}},
        #     limit=10, # Max. number of results to return
        #     output_fields=["id", "text"] # Fields to return
        # )
        return []
    results = milvus_client.query(
        collection_name=collection_name,
        ids=[hit['id'] for hit in hits],
        output_fields=["id", "title", "text", "source"]
    )
    rerank_texts = [f"文档名称：{result['title']} \n 文档内容：{result['text']}" for result in results]
    rerank_result = rerank(query_text, rerank_texts)
    top_indices = [item['index'] for item in rerank_result if item['score'] > 0.8]
    results = [results[i] for i in top_indices]
    documents = [Document(page_content=result['text'], metadata={
        "id": result['id'],
        "title": result['title'],
        "source": result['source']
    }) for result in results]
    return documents


def get_query_embedding(query_text: str) -> list[float]:
    config = get_or_creat_settings_ins()
    embedding = LLMFactory(config.embedding).BuildEmbedding(EmbeddingType.HUGGINGFACE)
    return embedding.embed_query(query_text)


if __name__ == '__main__':
    datas = hybrid_search_optimized(collection_name="knowledge_base_of_it_v3", query_text="云打印机如何使用")
    print(datas)
