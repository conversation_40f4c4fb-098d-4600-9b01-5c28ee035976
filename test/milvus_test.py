from src.helper.client.milvus import hybrid_search_optimized

if __name__ == "__main__":
    #x 测试混合搜索
    query_text = "数据库预案"
    results = hybrid_search_optimized(query_text, "knowledge_base_of_sre_v3")
    for i, result in enumerate(results):
        print(f"结果 #{i + 1}: {result['title']}")
        print(
            f"得分: {result['score']['final']:.4f} (向量: {result['score']['vector']:.4f}, BM25: {result['score']['bm25']:.4f}, 标题: {result['score']['title']:.4f}, 频率: {result['score']['frequency']:.4f})")
        print(f"来源: {result['source']}")
        print(f"内容摘要: {result['text'][:150]}...\n")
